<script lang="ts">
	/**
	 * Root Layout Component
	 * Enhanced authentication with comprehensive security
	 * Following Code Complete principles: Security-first, clear separation of concerns
	 */

	import '../app.css';
	import { onMount } from 'svelte';
	import {
		isAuthenticated,
		isLoading,
		enhancedAuthState
	} from '$lib/security/index.js';
	import { supabase } from '$lib/supabaseClient.js';
	import { AuthPage } from '$lib/components/auth/index.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';
	import RestaurantSetup from '$lib/components/auth/RestaurantSetup.svelte';
	import TallycaLayout from '$lib/components/TallycaLayout.svelte';

	let { children } = $props();

	// Progressive enhancement: Let app load first, then enhance with auth
	onMount(() => {
		console.log('🚀 App: Starting progressive authentication enhancement...');

		// Set up auth state listener - use provided session data to avoid redundant calls
		supabase.auth.onAuthStateChange(async (event, session) => {
			console.log('🔒 Auth State Change:', event, session ? 'Session exists' : 'No session');

			// Import and initialize security system with provided session data
			try {
				const { enhancedAuth } = await import('$lib/security/EnhancedAuthStore.js');
				// Pass the session data from the event to avoid redundant getSession() calls
				await enhancedAuth.handleAuthStateChangeWithSession(event, session);
			} catch (error) {
				console.error('🔒 Auth state handling failed:', error);
			}
		});

		console.log('✅ App: Progressive authentication setup complete');
	});
</script>

<!-- Authentication Guard -->
{#if $isLoading}
	<!-- Loading State -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<LoadingSpinner
			size="lg"
			message="Loading your account..."
		/>
	</div>
{:else if !$isAuthenticated}
	<!-- Authentication Required -->
	<AuthPage />
{:else if $enhancedAuthState.needsRestaurantSetup}
	<!-- Restaurant Setup Required -->
	<RestaurantSetup
		onComplete={async (_restaurantId) => {
			// Refresh auth state after restaurant creation
			console.log('🏢 Restaurant setup complete, refreshing auth state...');
			// The auth state listener will automatically handle the refresh
		}}
	/>
{:else}
	<!-- Authenticated App with Tallyca Layout -->
	<TallycaLayout>
		{@render children()}
	</TallycaLayout>
{/if}
