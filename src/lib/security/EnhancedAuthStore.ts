/**
 * Enhanced Authentication Store
 * Integrates all security services for comprehensive authentication management
 * Following Code Complete principles: Centralized security, comprehensive integration
 */

import { writable, derived, get } from 'svelte/store';
import { supabase } from '$lib/supabaseClient.js';
import { validateSecurity, type SecurityValidationResult } from './TokenValidationService.js';
import { sessionManager, type SessionState } from './SessionManager.js';
import { securityAudit } from './SecurityAuditService.js';
import { secureRPC } from './SecureRPCService.js';
import type { User, Session } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface EnhancedAuthState {
	// Core authentication
	isAuthenticated: boolean;
	isLoading: boolean;
	user: User | null;
	session: Session | null;
	
	// Organization context
	organizationId: string | null;
	userRole: string | null;
	membershipActive: boolean;
	needsRestaurantSetup: boolean;
	
	// Security status
	securityStatus: 'secure' | 'warning' | 'error';
	sessionTimeRemaining: number;
	needsRefresh: boolean;
	lastValidation: number;
	
	// Error handling
	error: string | null;
	lastError: string | null;
	retryCount: number;
}

export interface AuthenticationOptions {
	email: string;
	password: string;
	rememberMe?: boolean;
}

export interface AuthenticationResult {
	success: boolean;
	error?: string;
	requiresVerification?: boolean;
	user?: User;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialAuthState: EnhancedAuthState = {
	isAuthenticated: false,
	isLoading: true,
	user: null,
	session: null,
	organizationId: null,
	userRole: null,
	membershipActive: false,
	needsRestaurantSetup: false,
	securityStatus: 'error',
	sessionTimeRemaining: 0,
	needsRefresh: false,
	lastValidation: 0,
	error: null,
	lastError: null,
	retryCount: 0
};

// ============================================================================
// STORES
// ============================================================================

export const enhancedAuthState = writable<EnhancedAuthState>(initialAuthState);

// Derived stores for convenience
export const isAuthenticated = derived(enhancedAuthState, $state => $state.isAuthenticated);
export const isLoading = derived(enhancedAuthState, $state => $state.isLoading);
export const currentUser = derived(enhancedAuthState, $state => $state.user);
export const organizationContext = derived(enhancedAuthState, $state => ({
	organizationId: $state.organizationId,
	userRole: $state.userRole,
	membershipActive: $state.membershipActive
}));
export const securityStatus = derived(enhancedAuthState, $state => $state.securityStatus);

// ============================================================================
// ENHANCED AUTH SERVICE
// ============================================================================

export class EnhancedAuthService {
	private validationTimer: NodeJS.Timeout | null = null;
	private initialized = false;

	constructor() {
		this.setupSessionListener();
		this.setupSecurityMonitoring();
	}

	/**
	 * Initialize enhanced authentication system
	 * Following Code Complete: Comprehensive initialization, error handling
	 */
	async initialize(): Promise<void> {
		if (this.initialized) return;

		console.log('🔒 EnhancedAuthService: Initializing...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			// Initialize session manager
			await sessionManager.initialize();

			// Validate current security context
			await this.validateAndUpdateState();

			// Start periodic validation
			this.startPeriodicValidation();

			this.initialized = true;
			console.log('✅ EnhancedAuthService: Initialized successfully');

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
			console.error('🔒 EnhancedAuthService: Initialization error:', errorMessage);
			
			securityAudit.logEvent('security_violation', {
				error: errorMessage,
				context: 'initialization'
			}, 'high');

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Sign in with enhanced security validation
	 * Following Code Complete: Secure authentication, comprehensive logging
	 */
	async signIn(options: AuthenticationOptions): Promise<AuthenticationResult> {
		console.log('🔒 EnhancedAuthService: Signing in...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			// Attempt authentication
			const { data, error } = await supabase.auth.signInWithPassword({
				email: options.email,
				password: options.password
			});

			if (error) {
				// Log authentication failure
				securityAudit.logAuthFailure(options.email, error.message);
				
				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: error.message,
					lastError: error.message,
					retryCount: state.retryCount + 1
				}));

				return { success: false, error: error.message };
			}

			if (!data.user || !data.session) {
				const errorMsg = 'No user data returned';
				securityAudit.logAuthFailure(options.email, errorMsg);
				
				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: errorMsg
				}));

				return { success: false, error: errorMsg };
			}

			// Log successful authentication
			securityAudit.logAuthSuccess(data.user.id);

			// Validate and update state
			await this.validateAndUpdateState();

			console.log('✅ EnhancedAuthService: Sign in successful');
			return { success: true, user: data.user };

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
			console.error('🔒 EnhancedAuthService: Sign in error:', errorMessage);
			
			securityAudit.logEvent('authentication_failure', {
				email: options.email,
				error: errorMessage
			}, 'medium');

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				lastError: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	}

	/**
	 * Sign up with enhanced validation
	 * Following Code Complete: Secure registration, validation
	 */
	async signUp(options: AuthenticationOptions): Promise<AuthenticationResult> {
		console.log('🔒 EnhancedAuthService: Signing up...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			const { data, error } = await supabase.auth.signUp({
				email: options.email,
				password: options.password
			});

			if (error) {
				securityAudit.logEvent('authentication_failure', {
					email: options.email,
					error: error.message,
					type: 'signup'
				}, 'medium');

				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: error.message
				}));

				return { success: false, error: error.message };
			}

			// Log successful signup
			if (data.user) {
				securityAudit.logEvent('authentication_success', {
					userId: data.user.id,
					type: 'signup'
				}, 'low', data.user.id);
			}

			enhancedAuthState.update(state => ({ ...state, isLoading: false }));

			return {
				success: true,
				requiresVerification: !data.session,
				user: data.user || undefined
			};

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
			console.error('🔒 EnhancedAuthService: Sign up error:', errorMessage);
			
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	}

	/**
	 * Sign out with comprehensive cleanup
	 * Following Code Complete: Secure logout, complete state clearing
	 */
	async signOut(): Promise<void> {
		console.log('🔒 EnhancedAuthService: Signing out...');
		
		const currentState = get(enhancedAuthState);
		
		try {
			// Log session end
			if (currentState.user) {
				securityAudit.logEvent('session_end', {
					userId: currentState.user.id,
					reason: 'user_logout'
				}, 'low', currentState.user.id, currentState.organizationId);
			}

			// End session through session manager
			await sessionManager.endSession('user_logout');

			// Sign out from Supabase
			await supabase.auth.signOut();

			// Clear all state
			this.clearAuthState();

			console.log('✅ EnhancedAuthService: Sign out successful');

		} catch (error) {
			console.error('🔒 EnhancedAuthService: Sign out error:', error);
			// Force clear state even if signout fails
			this.clearAuthState();
		}
	}

	/**
	 * Initialize without immediate validation (for smart auth handling)
	 * Following Code Complete: Lazy initialization, avoid circular dependencies
	 */
	async initializeWithoutValidation(): Promise<void> {
		if (this.initialized) return;

		console.log('🔒 EnhancedAuthService: Initializing without validation...');

		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			// Initialize session manager without validation
			await sessionManager.initialize();

			// Start periodic validation (but don't validate immediately)
			this.startPeriodicValidation();

			this.initialized = true;
			console.log('✅ EnhancedAuthService: Initialized without validation successfully');

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
			console.error('🔒 EnhancedAuthService: Initialization error:', errorMessage);

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Update state with provided session data (avoid redundant getSession calls)
	 * Following Code Complete: Efficient data usage, direct state management
	 */
	async updateStateWithProvidedSession(session: Session): Promise<void> {
		try {
			console.log('🔒 EnhancedAuthService: Updating state with provided session data...');

			// Get organization context using the provided session
			const organizationContext = await this.getOrganizationContextForUser(session.user.id);

			const newState: Partial<EnhancedAuthState> = {
				isAuthenticated: true,
				isLoading: false,
				user: session.user,
				session: session,
				organizationId: organizationContext.organizationId,
				userRole: organizationContext.userRole,
				membershipActive: organizationContext.membershipActive,
				needsRestaurantSetup: !organizationContext.organizationId,
				securityStatus: this.getSecurityStatusFromSession(session),
				sessionTimeRemaining: this.calculateTimeRemaining(session),
				needsRefresh: this.shouldRefreshSession(session),
				lastValidation: Date.now(),
				error: null,
				retryCount: 0
			};

			enhancedAuthState.update(state => ({ ...state, ...newState }));
			console.log('✅ EnhancedAuthService: State updated with provided session data');

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Session state update failed';
			console.error('🔒 EnhancedAuthService: Session state update error:', errorMessage);

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Validate and update authentication state (legacy method with smart retry)
	 * Following Code Complete: Comprehensive validation, state synchronization
	 */
	async validateAndUpdateState(): Promise<void> {
		try {
			const validation = await validateSecurity();
			const sessionStatus = sessionManager.getStatus();

			const newState: Partial<EnhancedAuthState> = {
				isAuthenticated: validation.isValid,
				isLoading: false,
				user: validation.user,
				session: validation.session,
				organizationId: validation.organizationContext.organizationId,
				userRole: validation.organizationContext.userRole,
				membershipActive: validation.organizationContext.membershipActive,
				needsRestaurantSetup: validation.isValid && !validation.organizationContext.organizationId,
				securityStatus: this.getSecurityStatus(validation),
				sessionTimeRemaining: sessionStatus.timeRemaining,
				needsRefresh: validation.needsRefresh,
				lastValidation: Date.now(),
				error: validation.error || null,
				retryCount: 0
			};

			enhancedAuthState.update(state => ({ ...state, ...newState }));

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Validation failed';
			console.error('🔒 EnhancedAuthService: Validation error:', errorMessage);
			
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Handle auth state changes with provided session data (smart approach)
	 * Following Code Complete: Event-driven architecture, efficient data usage
	 */
	async handleAuthStateChangeWithSession(event: string, session: Session | null): Promise<void> {
		console.log(`🔒 EnhancedAuthService: Handling auth state change with session: ${event}`);

		try {
			// Initialize if not already done
			if (!this.initialized) {
				await this.initializeWithoutValidation();
			}

			// Update state based on the auth event using provided session data
			switch (event) {
				case 'INITIAL_SESSION':
				case 'SIGNED_IN':
				case 'TOKEN_REFRESHED':
					if (session) {
						console.log('✅ EnhancedAuthService: Valid session provided, updating state directly...');
						await this.updateStateWithProvidedSession(session);
					} else {
						console.log('🔒 EnhancedAuthService: No session in auth event, clearing state...');
						this.clearAuthState();
					}
					break;

				case 'SIGNED_OUT':
					console.log('🔒 EnhancedAuthService: User signed out, clearing state...');
					this.clearAuthState();
					break;

				default:
					console.log(`🔒 EnhancedAuthService: Unhandled auth event: ${event}`);
					break;
			}

		} catch (error) {
			console.error('🔒 EnhancedAuthService: Auth state change handling failed:', error);

			// On error, ensure we're in a safe state
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: error instanceof Error ? error.message : 'Auth state change failed'
			}));
		}
	}

	/**
	 * Handle auth state changes from Supabase (legacy method)
	 * Following Code Complete: Event-driven architecture, progressive enhancement
	 * @deprecated Use handleAuthStateChangeWithSession instead
	 */
	async handleAuthStateChange(event: string, session: Session | null): Promise<void> {
		console.log(`🔒 EnhancedAuthService: Handling auth state change (legacy): ${event}`);
		// Delegate to the new method
		await this.handleAuthStateChangeWithSession(event, session);
	}

	/**
	 * Check if user has required role
	 * Following Code Complete: Authorization validation, role-based access
	 */
	hasRole(requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin'): boolean {
		const state = get(enhancedAuthState);
		
		if (!state.isAuthenticated || !state.organizationId || !state.membershipActive) {
			return false;
		}

		const roleHierarchy = ['viewer', 'editor', 'admin', 'superadmin'];
		const userRoleIndex = roleHierarchy.indexOf(state.userRole || '');
		const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

		return userRoleIndex >= requiredRoleIndex;
	}

	/**
	 * Execute secure RPC call with authentication
	 * Following Code Complete: Secure API access, integrated authentication
	 */
	async secureCall<T = any>(
		functionName: string,
		parameters: any = {},
		requireOrganization = false,
		requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin' = 'viewer'
	): Promise<{ data: T | null; error: any; success: boolean }> {
		const result = await secureRPC<T>(functionName, parameters, {
			requireOrganization,
			requiredRole
		});

		// Log data access
		const state = get(enhancedAuthState);
		securityAudit.logEvent('data_access', {
			function: functionName,
			success: !result.error,
			organizationRequired: requireOrganization,
			roleRequired: requiredRole
		}, 'low', state.user?.id, state.organizationId);

		return {
			data: result.data,
			error: result.error,
			success: !result.error
		};
	}

	// ============================================================================
	// PRIVATE METHODS
	// ============================================================================

	/**
	 * Setup session state listener
	 * Following Code Complete: Event-driven architecture, state synchronization
	 */
	private setupSessionListener(): void {
		sessionManager.addEventListener((event) => {
			switch (event.type) {
				case 'session_start':
				case 'session_refresh':
					this.validateAndUpdateState();
					break;
				
				case 'session_end':
				case 'session_expired':
					this.clearAuthState();
					break;
			}
		});
	}

	/**
	 * Setup security monitoring
	 * Following Code Complete: Security monitoring, event handling
	 */
	private setupSecurityMonitoring(): void {
		securityAudit.addAlertListener((alert) => {
			if (alert.severity === 'critical') {
				console.error('🚨 Critical Security Alert:', alert.message);
				// In production, could trigger additional security measures
			}
		});
	}

	/**
	 * Start periodic validation
	 * Following Code Complete: Proactive validation, automated monitoring
	 */
	private startPeriodicValidation(): void {
		if (this.validationTimer) {
			clearInterval(this.validationTimer);
		}

		this.validationTimer = setInterval(() => {
			const state = get(enhancedAuthState);
			if (state.isAuthenticated) {
				this.validateAndUpdateState();
			}
		}, 60000); // Validate every minute
	}

	/**
	 * Get security status based on validation
	 * Following Code Complete: Status assessment, clear categorization
	 */
	private getSecurityStatus(validation: SecurityValidationResult): 'secure' | 'warning' | 'error' {
		if (!validation.isValid) {
			return 'error';
		}

		if (validation.needsRefresh || validation.timeUntilExpiry < 5 * 60 * 1000) {
			return 'warning';
		}

		return 'secure';
	}

	/**
	 * Get organization context for a user (efficient method)
	 * Following Code Complete: Efficient data access, clear purpose
	 */
	private async getOrganizationContextForUser(userId: string): Promise<{
		organizationId: string | null;
		userRole: string | null;
		membershipActive: boolean;
	}> {
		try {
			// Use secure RPC to get user organizations (using existing function)
			const result = await secureRPC('get_user_organizations');

			if (result.error) {
				console.warn('🔒 EnhancedAuthService: Failed to get organization context:', result.error);
				return { organizationId: null, userRole: null, membershipActive: false };
			}

			// Handle case where no organizations exist
			if (!result.data || !Array.isArray(result.data) || result.data.length === 0) {
				console.log('🔒 EnhancedAuthService: No organizations found for user');
				return { organizationId: null, userRole: null, membershipActive: false };
			}

			// Use the first active organization (consistent with TokenValidationService)
			const activeOrg = result.data.find((org: any) => org.membership_active) || result.data[0];

			return {
				organizationId: activeOrg.id || null,
				userRole: activeOrg.user_role || null,
				membershipActive: activeOrg.membership_active ?? true
			};
		} catch (error) {
			console.warn('🔒 EnhancedAuthService: Organization context error:', error);
			return { organizationId: null, userRole: null, membershipActive: false };
		}
	}

	/**
	 * Get security status from session data
	 * Following Code Complete: Direct calculation, efficient processing
	 */
	private getSecurityStatusFromSession(session: Session): 'secure' | 'warning' | 'error' {
		const currentTime = Date.now();
		const expiryTime = session.expires_at ? session.expires_at * 1000 : 0;
		const timeUntilExpiry = expiryTime - currentTime;

		if (timeUntilExpiry <= 0) {
			return 'error';
		}

		if (timeUntilExpiry < 5 * 60 * 1000) { // Less than 5 minutes
			return 'warning';
		}

		return 'secure';
	}

	/**
	 * Calculate time remaining for session
	 * Following Code Complete: Clear calculation, defensive programming
	 */
	private calculateTimeRemaining(session: Session): number {
		const currentTime = Date.now();
		const expiryTime = session.expires_at ? session.expires_at * 1000 : 0;
		return Math.max(0, expiryTime - currentTime);
	}

	/**
	 * Check if session should be refreshed
	 * Following Code Complete: Clear logic, configurable threshold
	 */
	private shouldRefreshSession(session: Session): boolean {
		const timeRemaining = this.calculateTimeRemaining(session);
		return timeRemaining < 5 * 60 * 1000; // Refresh if less than 5 minutes remaining
	}

	/**
	 * Clear authentication state
	 * Following Code Complete: Complete state cleanup, security
	 */
	private clearAuthState(): void {
		enhancedAuthState.set({
			...initialAuthState,
			isLoading: false
		});

		if (this.validationTimer) {
			clearInterval(this.validationTimer);
			this.validationTimer = null;
		}
	}
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Global enhanced authentication service instance
 * Following Code Complete: Singleton pattern, centralized authentication
 */
export const enhancedAuth = new EnhancedAuthService();
