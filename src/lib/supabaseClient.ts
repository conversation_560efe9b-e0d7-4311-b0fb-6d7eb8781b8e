import { createClient } from '@supabase/supabase-js';
import { browser } from '$app/environment';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './env.js';

/**
 * Enhanced Supabase Client with Initialization Tracking
 * Following Code Complete: Defensive programming, proper initialization
 */

// Client initialization state
let clientInitialized = false;
let initializationPromise: Promise<void> | null = null;

// Auto-initialize client when module loads in browser
if (typeof window !== 'undefined') {
	// Mark as initialized immediately in browser context
	// The Supabase client is ready to use as soon as it's created
	clientInitialized = true;
	console.log('🔧 Supabase: Client auto-initialized in browser context');
}

// Create Supabase client with enhanced configuration
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
	auth: {
		// Reduce initial session check timeout for faster startup
		detectSessionInUrl: true,
		persistSession: true,
		autoRefreshToken: true,
		// Add storage key for better session management
		storageKey: 'work-scheduler-auth'
	},
	// Add global timeout configuration
	global: {
		headers: {
			'X-Client-Info': 'work-scheduler-web'
		}
	}
});

/**
 * Initialize Supabase client (simplified - client is ready immediately)
 * Following Code Complete: Explicit initialization, avoid circular dependencies
 */
export async function initializeSupabaseClient(): Promise<void> {
	// Return existing promise if already initializing
	if (initializationPromise) {
		return initializationPromise;
	}

	// Return immediately if already initialized
	if (clientInitialized) {
		return Promise.resolve();
	}

	// Create initialization promise
	initializationPromise = new Promise<void>(async (resolve) => {
		// In browser environment, client is already ready
		if (browser) {
			console.log('🔧 Supabase: Client already initialized');
			clientInitialized = true;
			resolve();
			return;
		}

		// In SSR context, mark as initialized
		console.log('🔧 Supabase: SSR client initialized');
		clientInitialized = true;
		resolve();
	});

	return initializationPromise;
}

/**
 * Check if Supabase client is ready for use
 * Following Code Complete: Clear state checking
 */
export function isSupabaseClientReady(): boolean {
	return clientInitialized;
}

/**
 * Get Supabase client with initialization check
 * Following Code Complete: Defensive programming, clear error messages
 */
export function getSupabaseClient() {
	if (!browser) {
		// In SSR context, return client directly
		return supabase;
	}

	if (!clientInitialized) {
		console.warn('⚠️ Supabase: Client accessed before initialization. Call initializeSupabaseClient() first.');
	}

	return supabase;
}